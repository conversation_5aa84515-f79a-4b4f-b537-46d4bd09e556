import { FC } from 'react';

interface IconCoffeeProps {
    className?: string;
    fill?: boolean;
    duotone?: boolean;
}

const IconCoffee: FC<IconCoffeeProps> = ({ className, fill = false, duotone = true }) => {
    return (
        <>
            {!fill ? (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
                    <path
                        d="M2.3153 12.6978C2.26536 12.2706 2.2404 12.057 2.2509 11.8809C2.30599 10.9577 2.98677 10.1928 3.89725 10.0309C4.07094 10 4.286 10 4.71612 10H15.2838C15.7139 10 15.929 10 16.1027 10.0309C17.0132 10.1928 17.694 10.9577 17.749 11.8809C17.7595 12.057 17.7346 12.2706 17.6846 12.6978L17.284 16.1258C17.1031 17.6729 16.2764 19.0714 15.0081 19.9757C14.0736 20.6419 12.9546 21 11.8069 21H8.19303C7.04537 21 5.9263 20.6419 4.99182 19.9757C3.72352 19.0714 2.89681 17.6729 2.71598 16.1258L2.3153 12.6978Z"
                        stroke="currentColor"
                        strokeWidth="1.5"
                    />
                    <path opacity={duotone ? '0.5' : '1'} d="M17 17H19C20.6569 17 22 15.6569 22 14C22 12.3431 20.6569 11 19 11H17.5" stroke="currentColor" strokeWidth="1.5" />
                    <path
                        opacity={duotone ? '0.5' : '1'}
                        d="M10.0002 2C9.44787 2.55228 9.44787 3.44772 10.0002 4C10.5524 4.55228 10.5524 5.44772 10.0002 6"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                    <path
                        d="M4.99994 7.5L5.11605 7.38388C5.62322 6.87671 5.68028 6.0738 5.24994 5.5C4.81959 4.9262 4.87665 4.12329 5.38382 3.61612L5.49994 3.5"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                    <path
                        d="M14.4999 7.5L14.6161 7.38388C15.1232 6.87671 15.1803 6.0738 14.7499 5.5C14.3196 4.9262 14.3767 4.12329 14.8838 3.61612L14.9999 3.5"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                </svg>
            ) : (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M2.2509 11.8809C2.2404 12.057 2.26536 12.2706 2.3153 12.6978L2.71598 16.1258C2.89681 17.6729 3.72352 19.0714 4.99182 19.9757C5.9263 20.6419 7.04537 21 8.19303 21H11.8069C12.9546 21 14.0736 20.6419 15.0081 19.9757C15.8116 19.4028 16.4378 18.6317 16.8349 17.75H19C21.0711 17.75 22.75 16.0711 22.75 14C22.75 11.9289 21.0711 10.25 19 10.25H16.7212C16.5325 10.1455 16.3244 10.0703 16.1027 10.0309C15.929 10 15.7139 10 15.2838 10H4.71612C4.286 10 4.07094 10 3.89725 10.0309C2.98677 10.1928 2.30599 10.9577 2.2509 11.8809ZM17.7369 11.75C17.7424 11.7932 17.7464 11.8369 17.749 11.8809C17.7595 12.057 17.7346 12.2706 17.6847 12.6977L17.284 16.1258C17.2791 16.1673 17.2738 16.2087 17.268 16.25H19C20.2426 16.25 21.25 15.2426 21.25 14C21.25 12.7574 20.2426 11.75 19 11.75H17.7369Z"
                        fill="currentColor"
                    />
                    <g opacity={duotone ? '0.5' : '1'}>
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M10.5307 1.46967C10.8236 1.76256 10.8236 2.23744 10.5307 2.53033C10.2713 2.78972 10.2713 3.21028 10.5307 3.46967C11.3758 4.31485 11.3758 5.68515 10.5307 6.53033C10.2378 6.82322 9.7629 6.82322 9.47001 6.53033C9.17712 6.23744 9.17712 5.76256 9.47001 5.46967C9.7294 5.21028 9.7294 4.78972 9.47001 4.53033C8.62483 3.68515 8.62483 2.31485 9.47001 1.46967C9.7629 1.17678 10.2378 1.17678 10.5307 1.46967Z"
                            fill="currentColor"
                        />
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M6.03052 2.96967C6.32341 3.26256 6.32341 3.73744 6.03052 4.03033L5.9144 4.14645C5.67115 4.3897 5.64379 4.77479 5.85019 5.05C6.50448 5.92239 6.41772 7.14313 5.64664 7.91421L5.53052 8.03033C5.23763 8.32322 4.76275 8.32322 4.46986 8.03033C4.17697 7.73744 4.17697 7.26256 4.46986 6.96967L4.58598 6.85355C4.82923 6.6103 4.85659 6.22521 4.65019 5.95C3.9959 5.07761 4.08266 3.85687 4.85374 3.08579L4.96986 2.96967C5.26275 2.67678 5.73763 2.67678 6.03052 2.96967Z"
                            fill="currentColor"
                        />
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M15.5305 2.96967C15.8234 3.26256 15.8234 3.73744 15.5305 4.03033L15.4144 4.14645C15.1712 4.3897 15.1438 4.77479 15.3502 5.05C16.0045 5.92239 15.9177 7.14313 15.1466 7.91421L15.0305 8.03033C14.7376 8.32322 14.2628 8.32322 13.9699 8.03033C13.677 7.73744 13.677 7.26256 13.9699 6.96967L14.086 6.85355C14.3292 6.6103 14.3566 6.22521 14.1502 5.95C13.4959 5.07761 13.5827 3.85687 14.3537 3.08579L14.4699 2.96967C14.7628 2.67678 15.2376 2.67678 15.5305 2.96967Z"
                            fill="currentColor"
                        />
                    </g>
                </svg>
            )}
        </>
    );
};

export default IconCoffee;

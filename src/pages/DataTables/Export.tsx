import { DataTable, DataTableSortStatus } from 'mantine-datatable';
import { useEffect, useState } from 'react';
import { sortBy } from 'lodash-es';
import { downloadExcel } from 'react-export-table-to-excel';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '../../store/themeConfigSlice';
import IconBell from '../../components/Icon/IconBell';
import IconFile from '../../components/Icon/IconFile';
import IconPrinter from '../../components/Icon/IconPrinter';

const rowData = [
    {
        id: 1,
        firstName: 'Caroline',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        dob: '2004-05-28',
        // address: {
        //     street: '529 Scholes Street',
        //     city: 'Temperanceville',
        //     zipcode: 5235,
        //     geo: {
        //         lat: 23.806115,
        //         lng: 164.677197,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 39,
        company: 'POLARAX',
    },
    {
        id: 2,
        firstName: 'Celeste',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        dob: '1989-11-19',
        // address: {
        //     street: '639 Kimball Street',
        //     city: 'Bascom',
        //     zipcode: 8907,
        //     geo: {
        //         lat: 65.954483,
        //         lng: 98.906478,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 32,
        company: 'MANGLO',
    },
    {
        id: 3,
        firstName: 'Tillman',
        lastName: 'Forbes',
        email: '<EMAIL>',
        dob: '2016-09-05',
        // address: {
        //     street: '240 Vandalia Avenue',
        //     city: 'Thynedale',
        //     zipcode: 8994,
        //     geo: {
        //         lat: -34.949388,
        //         lng: -82.958111,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 26,
        company: 'APPLIDECK',
    },
    {
        id: 4,
        firstName: 'Daisy',
        lastName: 'Whitley',
        email: '<EMAIL>',
        dob: '1987-03-23',
        // address: {
        //     street: '350 Pleasant Place',
        //     city: 'Idledale',
        //     zipcode: 9369,
        //     geo: {
        //         lat: -54.458809,
        //         lng: -127.476556,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 21,
        company: 'VOLAX',
    },
    {
        id: 5,
        firstName: 'Weber',
        lastName: 'Bowman',
        email: '<EMAIL>',
        dob: '1983-02-24',
        // address: {
        //     street: '154 Conway Street',
        //     city: 'Broadlands',
        //     zipcode: 8131,
        //     geo: {
        //         lat: 54.501351,
        //         lng: -167.47138,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 26,
        company: 'ORBAXTER',
    },
    {
        id: 6,
        firstName: 'Buckley',
        lastName: 'Townsend',
        email: '<EMAIL>',
        dob: '2011-05-29',
        // address: {
        //     street: '131 Guernsey Street',
        //     city: 'Vallonia',
        //     zipcode: 6779,
        //     geo: {
        //         lat: -2.681655,
        //         lng: 3.528942,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 40,
        company: 'OPPORTECH',
    },
    {
        id: 7,
        firstName: 'Latoya',
        lastName: 'Bradshaw',
        email: '<EMAIL>',
        dob: '2010-11-23',
        // address: {
        //     street: '668 Lenox Road',
        //     city: 'Lowgap',
        //     zipcode: 992,
        //     geo: {
        //         lat: 36.026423,
        //         lng: 130.412198,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 24,
        company: 'GORGANIC',
    },
    {
        id: 8,
        firstName: 'Kate',
        lastName: 'Lindsay',
        email: '<EMAIL>',
        dob: '1987-07-02',
        // address: {
        //     street: '773 Harrison Avenue',
        //     city: 'Carlton',
        //     zipcode: 5909,
        //     geo: {
        //         lat: 42.464724,
        //         lng: -12.948403,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 24,
        company: 'AVIT',
    },
    {
        id: 9,
        firstName: 'Marva',
        lastName: 'Sandoval',
        email: '<EMAIL>',
        dob: '2010-11-02',
        // address: {
        //     street: '200 Malta Street',
        //     city: 'Tuskahoma',
        //     zipcode: 1292,
        //     geo: {
        //         lat: -52.206169,
        //         lng: 74.19452,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 28,
        company: 'QUILCH',
    },
    {
        id: 10,
        firstName: 'Decker',
        lastName: 'Russell',
        email: '<EMAIL>',
        dob: '1994-04-21',
        // address: {
        //     street: '708 Bath Avenue',
        //     city: 'Coultervillle',
        //     zipcode: 1268,
        //     geo: {
        //         lat: -41.550295,
        //         lng: -146.598075,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 27,
        company: 'MEMORA',
    },
    {
        id: 11,
        firstName: 'Odom',
        lastName: 'Mills',
        email: '<EMAIL>',
        dob: '2010-01-24',
        // address: {
        //     street: '907 Blake Avenue',
        //     city: 'Churchill',
        //     zipcode: 4400,
        //     geo: {
        //         lat: -56.061694,
        //         lng: -130.238523,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 34,
        company: 'ZORROMOP',
    },
    {
        id: 12,
        firstName: 'Sellers',
        lastName: 'Walters',
        email: '<EMAIL>',
        dob: '1975-11-12',
        // address: {
        //     street: '978 Oakland Place',
        //     city: 'Gloucester',
        //     zipcode: 3802,
        //     geo: {
        //         lat: 11.732587,
        //         lng: 96.118099,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 28,
        company: 'ORBOID',
    },
    {
        id: 13,
        firstName: 'Wendi',
        lastName: 'Powers',
        email: '<EMAIL>',
        dob: '1979-06-02',
        // address: {
        //     street: '376 Greenpoint Avenue',
        //     city: 'Elliott',
        //     zipcode: 9149,
        //     geo: {
        //         lat: -78.159578,
        //         lng: -9.835103,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 31,
        company: 'SNORUS',
    },
    {
        id: 14,
        firstName: 'Sophie',
        lastName: 'Horn',
        email: '<EMAIL>',
        dob: '2018-09-20',
        // address: {
        //     street: '343 Doughty Street',
        //     city: 'Homestead',
        //     zipcode: 330,
        //     geo: {
        //         lat: 65.484087,
        //         lng: 137.413998,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 22,
        company: 'XTH',
    },
    {
        id: 15,
        firstName: 'Levine',
        lastName: 'Rodriquez',
        email: '<EMAIL>',
        dob: '1973-02-08',
        // address: {
        //     street: '643 Allen Avenue',
        //     city: 'Weedville',
        //     zipcode: 8931,
        //     geo: {
        //         lat: -63.185586,
        //         lng: 117.327808,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 27,
        company: 'COMTRACT',
    },
    {
        id: 16,
        firstName: 'Little',
        lastName: 'Hatfield',
        email: '<EMAIL>',
        dob: '2012-01-03',
        // address: {
        //     street: '194 Anthony Street',
        //     city: 'Williston',
        //     zipcode: 7456,
        //     geo: {
        //         lat: 47.480837,
        //         lng: 6.085909,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 33,
        company: 'ZIDANT',
    },
    {
        id: 17,
        firstName: 'Larson',
        lastName: 'Kelly',
        email: '<EMAIL>',
        dob: '2010-06-14',
        // address: {
        //     street: '978 Indiana Place',
        //     city: 'Innsbrook',
        //     zipcode: 639,
        //     geo: {
        //         lat: -71.766732,
        //         lng: 150.854345,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 20,
        company: 'SUREPLEX',
    },
    {
        id: 18,
        firstName: 'Kendra',
        lastName: 'Molina',
        email: '<EMAIL>',
        dob: '2002-07-19',
        // address: {
        //     street: '567 Charles Place',
        //     city: 'Kimmell',
        //     zipcode: 1966,
        //     geo: {
        //         lat: 50.765816,
        //         lng: -117.106499,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 31,
        company: 'DANJA',
    },
    {
        id: 19,
        firstName: 'Ebony',
        lastName: 'Livingston',
        email: '<EMAIL>',
        dob: '1994-10-18',
        // address: {
        //     street: '284 Cass Place',
        //     city: 'Navarre',
        //     zipcode: 948,
        //     geo: {
        //         lat: 65.271256,
        //         lng: -83.064729,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 33,
        company: 'EURON',
    },
    {
        id: 20,
        firstName: 'Kaufman',
        lastName: 'Rush',
        email: '<EMAIL>',
        dob: '2011-07-10',
        // address: {
        //     street: '408 Kingsland Avenue',
        //     city: 'Beaulieu',
        //     zipcode: 7911,
        //     geo: {
        //         lat: 41.513153,
        //         lng: 54.821641,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 39,
        company: 'ILLUMITY',
    },
    {
        id: 21,
        firstName: 'Frank',
        lastName: 'Hays',
        email: '<EMAIL>',
        dob: '2005-06-15',
        // address: {
        //     street: '973 Caton Place',
        //     city: 'Dargan',
        //     zipcode: 4104,
        //     geo: {
        //         lat: 63.314988,
        //         lng: -138.771323,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 31,
        company: 'SYBIXTEX',
    },
    {
        id: 22,
        firstName: 'Carmella',
        lastName: 'Mccarty',
        email: '<EMAIL>',
        dob: '1980-03-06',
        // address: {
        //     street: '919 Judge Street',
        //     city: 'Canby',
        //     zipcode: 8283,
        //     geo: {
        //         lat: 9.198597,
        //         lng: -138.809971,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 21,
        company: 'ZEDALIS',
    },
    {
        id: 23,
        firstName: 'Massey',
        lastName: 'Owen',
        email: '<EMAIL>',
        dob: '2012-03-01',
        // address: {
        //     street: '108 Seaview Avenue',
        //     city: 'Slovan',
        //     zipcode: 3599,
        //     geo: {
        //         lat: -74.648318,
        //         lng: 99.620699,
        //     },
        // },
        phone: '+****************',
        isActive: false,
        age: 40,
        company: 'DYNO',
    },
    {
        id: 24,
        firstName: 'Lottie',
        lastName: 'Lowery',
        email: '<EMAIL>',
        dob: '1982-10-10',
        // address: {
        //     street: '557 Meserole Avenue',
        //     city: 'Fowlerville',
        //     zipcode: 4991,
        //     geo: {
        //         lat: 54.811546,
        //         lng: -20.996515,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 36,
        company: 'MULTIFLEX',
    },
    {
        id: 25,
        firstName: 'Addie',
        lastName: 'Luna',
        email: '<EMAIL>',
        dob: '1988-05-01',
        // address: {
        //     street: '688 Bulwer Place',
        //     city: 'Harmon',
        //     zipcode: 7664,
        //     geo: {
        //         lat: -12.762766,
        //         lng: -39.924497,
        //     },
        // },
        phone: '+****************',
        isActive: true,
        age: 32,
        company: 'PHARMACON',
    },
];

const col = ['id', 'firstName', 'lastName', 'company', 'age', 'dob', 'email', 'phone'];

const Export = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Export Table'));
    });
    const [page, setPage] = useState(1);
    const PAGE_SIZES = [10, 20, 30, 50, 100];
    const [pageSize, setPageSize] = useState(PAGE_SIZES[0]);
    const [initialRecords, setInitialRecords] = useState(sortBy(rowData, 'id'));
    const [recordsData, setRecordsData] = useState(initialRecords);

    const [search, setSearch] = useState('');
    const [sortStatus, setSortStatus] = useState<DataTableSortStatus>({ columnAccessor: 'id', direction: 'asc' });

    useEffect(() => {
        setPage(1);
    }, [pageSize]);

    useEffect(() => {
        const from = (page - 1) * pageSize;
        const to = from + pageSize;
        setRecordsData([...initialRecords.slice(from, to)]);
    }, [page, pageSize, initialRecords]);

    useEffect(() => {
        setInitialRecords(() => {
            return rowData.filter((item: any) => {
                return (
                    item.id.toString().includes(search.toLowerCase()) ||
                    item.firstName.toLowerCase().includes(search.toLowerCase()) ||
                    item.lastName.toLowerCase().includes(search.toLowerCase()) ||
                    item.company.toLowerCase().includes(search.toLowerCase()) ||
                    item.email.toLowerCase().includes(search.toLowerCase()) ||
                    item.age.toString().toLowerCase().includes(search.toLowerCase()) ||
                    item.dob.toLowerCase().includes(search.toLowerCase()) ||
                    item.phone.toLowerCase().includes(search.toLowerCase())
                );
            });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [search]);

    useEffect(() => {
        const data = sortBy(initialRecords, sortStatus.columnAccessor);
        setInitialRecords(sortStatus.direction === 'desc' ? data.reverse() : data);
        setPage(1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [sortStatus]);
    const header = ['Id', 'Firstname', 'Lastname', 'Email', 'Start Date', 'Phone No.', 'Age', 'Company'];

    const formatDate = (date: any) => {
        if (date) {
            const dt = new Date(date);
            const month = dt.getMonth() + 1 < 10 ? '0' + (dt.getMonth() + 1) : dt.getMonth() + 1;
            const day = dt.getDate() < 10 ? '0' + dt.getDate() : dt.getDate();
            return day + '/' + month + '/' + dt.getFullYear();
        }
        return '';
    };

    function handleDownloadExcel() {
        downloadExcel({
            fileName: 'table',
            sheet: 'react-export-table-to-excel',
            tablePayload: {
                header,
                body: rowData,
            },
        });
    }

    const exportTable = (type: any) => {
        let columns: any = col;
        let records = rowData;
        let filename = 'table';

        let newVariable: any;
        newVariable = window.navigator;

        if (type === 'csv') {
            let coldelimiter = ';';
            let linedelimiter = '\n';
            let result = columns
                .map((d: any) => {
                    return capitalize(d);
                })
                .join(coldelimiter);
            result += linedelimiter;
            // eslint-disable-next-line array-callback-return
            records.map((item: any) => {
                // eslint-disable-next-line array-callback-return
                columns.map((d: any, index: any) => {
                    if (index > 0) {
                        result += coldelimiter;
                    }
                    let val = item[d] ? item[d] : '';
                    result += val;
                });
                result += linedelimiter;
            });

            if (result == null) return;
            if (!result.match(/^data:text\/csv/i) && !newVariable.msSaveOrOpenBlob) {
                var data = 'data:application/csv;charset=utf-8,' + encodeURIComponent(result);
                var link = document.createElement('a');
                link.setAttribute('href', data);
                link.setAttribute('download', filename + '.csv');
                link.click();
            } else {
                var blob = new Blob([result]);
                if (newVariable.msSaveOrOpenBlob) {
                    newVariable.msSaveBlob(blob, filename + '.csv');
                }
            }
        } else if (type === 'print') {
            var rowhtml = '<p>' + filename + '</p>';
            rowhtml +=
                '<table style="width: 100%; " cellpadding="0" cellcpacing="0"><thead><tr style="color: #515365; background: #eff5ff; -webkit-print-color-adjust: exact; print-color-adjust: exact; "> ';
            // eslint-disable-next-line array-callback-return
            columns.map((d: any) => {
                rowhtml += '<th>' + capitalize(d) + '</th>';
            });
            rowhtml += '</tr></thead>';
            rowhtml += '<tbody>';

            // eslint-disable-next-line array-callback-return
            records.map((item: any) => {
                rowhtml += '<tr>';
                // eslint-disable-next-line array-callback-return
                columns.map((d: any) => {
                    let val = item[d] ? item[d] : '';
                    rowhtml += '<td>' + val + '</td>';
                });
                rowhtml += '</tr>';
            });
            rowhtml +=
                '<style>body {font-family:Arial; color:#495057;}p{text-align:center;font-size:18px;font-weight:bold;margin:15px;}table{ border-collapse: collapse; border-spacing: 0; }th,td{font-size:12px;text-align:left;padding: 4px;}th{padding:8px 4px;}tr:nth-child(2n-1){background:#f7f7f7; }</style>';
            rowhtml += '</tbody></table>';
            var winPrint: any = window.open('', '', 'left=0,top=0,width=1000,height=600,toolbar=0,scrollbars=0,status=0');
            winPrint.document.write('<title>Print</title>' + rowhtml);
            winPrint.document.close();
            winPrint.focus();
            winPrint.print();
        } else if (type === 'txt') {
            let coldelimiter = ',';
            let linedelimiter = '\n';
            let result = columns
                .map((d: any) => {
                    return capitalize(d);
                })
                .join(coldelimiter);
            result += linedelimiter;
            // eslint-disable-next-line array-callback-return
            records.map((item: any) => {
                // eslint-disable-next-line array-callback-return
                columns.map((d: any, index: any) => {
                    if (index > 0) {
                        result += coldelimiter;
                    }
                    let val = item[d] ? item[d] : '';
                    result += val;
                });
                result += linedelimiter;
            });

            if (result == null) return;
            if (!result.match(/^data:text\/txt/i) && !newVariable.msSaveOrOpenBlob) {
                var data1 = 'data:application/txt;charset=utf-8,' + encodeURIComponent(result);
                var link1 = document.createElement('a');
                link1.setAttribute('href', data1);
                link1.setAttribute('download', filename + '.txt');
                link1.click();
            } else {
                var blob1 = new Blob([result]);
                if (newVariable.msSaveOrOpenBlob) {
                    newVariable.msSaveBlob(blob1, filename + '.txt');
                }
            }
        }
    };

    const capitalize = (text: any) => {
        return text
            .replace('_', ' ')
            .replace('-', ' ')
            .toLowerCase()
            .split(' ')
            .map((s: any) => s.charAt(0).toUpperCase() + s.substring(1))
            .join(' ');
    };
    return (
        <div>
            <div className="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary">
                <div className="rounded-full bg-primary p-1.5 text-white ring-2 ring-primary/30 ltr:mr-3 rtl:ml-3">
                    <IconBell />
                </div>
                <span className="ltr:mr-3 rtl:ml-3">Documentation: </span>
                <a href="https://www.npmjs.com/package/mantine-datatable" target="_blank" className="block hover:underline">
                    https://www.npmjs.com/package/mantine-datatable
                </a>
            </div>

            <div className="panel mt-6">
                <div className="flex md:items-center justify-between md:flex-row flex-col mb-4.5 gap-5">
                    <div className="flex items-center flex-wrap">
                        <button type="button" onClick={() => exportTable('csv')} className="btn btn-primary btn-sm m-1 ">
                            <IconFile className="w-5 h-5 ltr:mr-2 rtl:ml-2" />
                            CSV
                        </button>
                        <button type="button" onClick={() => exportTable('txt')} className="btn btn-primary btn-sm m-1">
                            <IconFile className="w-5 h-5 ltr:mr-2 rtl:ml-2" />
                            TXT
                        </button>

                        <button type="button" className="btn btn-primary btn-sm m-1" onClick={handleDownloadExcel}>
                            <IconFile className="w-5 h-5 ltr:mr-2 rtl:ml-2" />
                            EXCEL
                        </button>

                        <button type="button" onClick={() => exportTable('print')} className="btn btn-primary btn-sm m-1">
                            <IconPrinter className="ltr:mr-2 rtl:ml-2" />
                            PRINT
                        </button>
                    </div>

                    <input type="text" className="form-input w-auto" placeholder="Search..." value={search} onChange={(e) => setSearch(e.target.value)} />
                </div>
                <div className="datatables">
                    <DataTable
                        highlightOnHover
                        className="whitespace-nowrap table-hover"
                        records={recordsData}
                        columns={[
                            { accessor: 'id', title: '#', sortable: true },
                            { accessor: 'firstName', sortable: true },
                            { accessor: 'lastName', sortable: true },
                            { accessor: 'company', title: 'Company', sortable: true },
                            { accessor: 'age', title: 'Age', sortable: true },
                            {
                                accessor: 'dob',
                                title: 'Start Date',
                                sortable: true,
                                render: ({ dob }) => <div>{formatDate(dob)}</div>,
                            },
                            { accessor: 'email', sortable: true },
                            { accessor: 'phone', sortable: true },
                        ]}
                        totalRecords={initialRecords.length}
                        recordsPerPage={pageSize}
                        page={page}
                        onPageChange={(p) => setPage(p)}
                        recordsPerPageOptions={PAGE_SIZES}
                        onRecordsPerPageChange={setPageSize}
                        sortStatus={sortStatus}
                        onSortStatusChange={setSortStatus}
                        minHeight={200}
                        paginationText={({ from, to, totalRecords }) => `Showing  ${from} to ${to} of ${totalRecords} entries`}
                    />
                </div>
            </div>
        </div>
    );
};

export default Export;

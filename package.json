{"name": "react-admin-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && tsc", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^1.9.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tippyjs/react": "^4.2.6", "@types/jest": "^27.5.2", "@types/node": "^16.18.94", "@types/react": "^18.2.74", "@types/react-dom": "^18.2.24", "@types/react-text-mask": "^5.4.14", "@x1mrdonut1x/nouislider-react": "^3.4.3", "apexcharts": "^3.48.0", "easymde": "^2.18.0", "flatpickr": "^4.6.13", "formik": "^2.4.5", "highlight.js": "^11.9.0", "lodash-es": "^4.17.21", "mantine-datatable": "^1.8.8", "nouislider": "^15.7.1", "nouislider-react": "^3.4.2", "react": "^18.2.0", "react-18-image-lightbox": "^5.1.4", "react-apexcharts": "^1.4.1", "react-click-away-listener": "^2.2.3", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-export-table-to-excel": "^1.0.6", "react-flatpickr": "^3.10.13", "react-images-uploading": "^3.1.7", "react-popper": "^2.3.0", "react-quill": "^2.0.0", "react-redux": "^8.1.3", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-simplemde-editor": "^5.2.0", "react-sortablejs": "^6.1.4", "react-text-mask": "^5.5.0", "sortablejs": "^1.15.2", "sweetalert2": "^11.10.7", "sweetalert2-react-content": "^5.0.7", "swiper": "^8.4.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "devDependencies": {"@headlessui/react": "^1.7.18", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.12", "@types/lodash-es": "^4.17.12", "@types/react": "^18.0.27", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.0.10", "@types/react-flatpickr": "^3.8.11", "@types/react-input-mask": "^3.0.5", "@types/sortablejs": "^1.15.8", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.19", "i18next": "^21.10.0", "i18next-browser-languagedetector": "^6.1.8", "i18next-http-backend": "^1.4.5", "postcss": "^8.4.38", "react-animate-height": "^3.2.3", "react-i18next": "^11.18.6", "react-perfect-scrollbar": "^1.5.8", "tailwindcss": "^3.4.3", "tippy.js": "^6.3.7", "typescript": "^4.9.3", "vite": "^4.5.3"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}